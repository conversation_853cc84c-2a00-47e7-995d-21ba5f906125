"""
财务凭证管理路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import FinancialVoucher, VoucherDetail, AccountingSubject
from app.forms.financial import FinancialVoucherForm
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text, func
from datetime import datetime, date
from decimal import Decimal


def get_school_pinyin_initials(school_name):
    """获取学校名称所有汉字的拼音首字母"""
    # 扩展的拼音首字母映射表
    pinyin_map = {
        # 常用地名
        '北': 'B', '京': 'J', '上': 'S', '海': 'H', '天': 'T', '津': 'J',
        '重': 'C', '庆': 'Q', '河': 'H', '山': 'S', '西': 'X', '东': 'D',
        '南': 'N', '湖': 'H', '广': 'G', '四': 'S', '川': 'C', '贵': 'G',
        '州': 'Z', '云': 'Y', '陕': 'S', '甘': 'G', '肃': 'S', '青': 'Q',
        '夏': 'X', '新': 'X', '疆': 'J', '台': 'T', '湾': 'W', '港': 'G',
        '澳': 'A', '门': 'M', '市': 'S', '区': 'Q', '县': 'X', '镇': 'Z',
        '乡': 'X', '村': 'C', '内': 'N', '蒙': 'M', '古': 'G', '辽': 'L',
        '宁': 'N', '吉': 'J', '林': 'L', '黑': 'H', '龙': 'L', '江': 'J',
        '苏': 'S', '浙': 'Z', '安': 'A', '徽': 'H', '福': 'F', '建': 'J',
        '香': 'X',

        # 朝阳相关
        '朝': 'C', '阳': 'Y',

        # 学校相关
        '学': 'X', '校': 'X', '小': 'X', '中': 'Z', '大': 'D', '高': 'G',
        '第': 'D', '一': 'Y', '二': 'E', '三': 'S', '四': 'S', '五': 'W',
        '六': 'L', '七': 'Q', '八': 'B', '九': 'J', '十': 'S',
        '实': 'S', '验': 'Y', '民': 'M', '族': 'Z', '外': 'W', '国': 'G',
        '语': 'Y', '职': 'Z', '业': 'Y', '技': 'J', '术': 'S', '师': 'S',
        '范': 'F', '幼': 'Y', '儿': 'E', '园': 'Y', '附': 'F', '属': 'S',

        # 其他常用字
        '人': 'R', '文': 'W', '理': 'L', '工': 'G', '农': 'N', '医': 'Y',
        '师': 'S', '范': 'F', '艺': 'Y', '体': 'T', '音': 'Y', '美': 'M',
        '科': 'K', '信': 'X', '息': 'X', '电': 'D', '子': 'Z', '机': 'J',
        '械': 'X', '建': 'J', '筑': 'Z', '环': 'H', '境': 'J', '材': 'C',
        '料': 'L', '化': 'H', '生': 'S', '物': 'W', '数': 'S', '统': 'T',
        '计': 'J', '经': 'J', '济': 'J', '管': 'G', '营': 'Y', '财': 'C',
        '会': 'H', '法': 'F', '政': 'Z', '历': 'L', '史': 'S', '哲': 'Z',
        '思': 'S', '想': 'X', '马': 'M', '克': 'K', '列': 'L', '毛': 'M',
        '邓': 'D', '江': 'J', '胡': 'H', '习': 'X', '近': 'J', '平': 'P',

        # 补充城市名
        '深': 'S', '圳': 'Z', '浦': 'P', '东': 'D', '南': 'N', '山': 'S',
        '宝': 'B', '安': 'A', '龙': 'L', '岗': 'G', '福': 'F', '田': 'T',
        '罗': 'L', '湖': 'H', '盐': 'Y', '田': 'T', '坪': 'P', '山': 'S',
        '光': 'G', '明': 'M', '大': 'D', '鹏': 'P', '龙': 'L', '华': 'H'
    }

    # 提取所有汉字的拼音首字母
    initials = ''
    for char in school_name:
        if char in pinyin_map:
            initials += pinyin_map[char]
        elif char.isalpha():
            initials += char.upper()
        # 跳过数字、标点符号等其他字符

    # 不限制长度，保留所有汉字的拼音首字母以确保唯一性
    # 例如：朝阳区第一小学 -> CYQDYXX
    # 例如：朝阳区实验中学 -> CYQSYZX
    return initials


def generate_voucher_number(user_area, voucher_date=None):
    """生成凭证号"""
    if voucher_date is None:
        voucher_date = date.today()

    # 获取学校名称的拼音首字母
    school_initials = get_school_pinyin_initials(user_area.name)

    # 生成凭证号前缀：学校拼音首字母 + PZ + 日期
    voucher_prefix = f"{school_initials}PZ{voucher_date.strftime('%Y%m%d')}"

    # 查找当日最大凭证号
    last_voucher = FinancialVoucher.query.filter(
        FinancialVoucher.area_id == user_area.id,
        FinancialVoucher.voucher_number.like(f'{voucher_prefix}%')
    ).order_by(FinancialVoucher.voucher_number.desc()).first()

    if last_voucher:
        last_number = int(last_voucher.voucher_number[-3:])
        voucher_number = f"{voucher_prefix}{last_number + 1:03d}"
    else:
        voucher_number = f"{voucher_prefix}001"

    return voucher_number


@financial_bp.route('/vouchers')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def vouchers_index(user_area):
    """财务凭证列表"""
    
    # 获取搜索参数
    keyword = request.args.get('keyword', '').strip()
    voucher_type = request.args.get('voucher_type', '').strip()
    status = request.args.get('status', '').strip()
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()
    
    # 构建查询
    query = FinancialVoucher.query.filter_by(area_id=user_area.id)
    
    if keyword:
        query = query.filter(
            db.or_(
                FinancialVoucher.voucher_number.like(f'%{keyword}%'),
                FinancialVoucher.summary.like(f'%{keyword}%')
            )
        )
    
    if voucher_type:
        query = query.filter_by(voucher_type=voucher_type)
    
    if status:
        query = query.filter_by(status=status)
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(FinancialVoucher.voucher_date >= start_date_obj)
        except ValueError:
            pass
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(FinancialVoucher.voucher_date <= end_date_obj)
        except ValueError:
            pass
    
    # 分页
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    
    vouchers = query.order_by(FinancialVoucher.voucher_date.desc(), 
                             FinancialVoucher.voucher_number.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('financial/vouchers/index.html',
                         vouchers=vouchers,
                         keyword=keyword,
                         voucher_type=voucher_type,
                         status=status,
                         start_date=start_date,
                         end_date=end_date)


@financial_bp.route('/vouchers/create', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def create_voucher(user_area):
    """创建财务凭证"""
    form = FinancialVoucherForm()
    
    if form.validate_on_submit():
        try:
            # 生成凭证号（使用新的生成函数）
            voucher_number = generate_voucher_number(user_area, date.today())

            # 使用字符串格式化避免参数绑定问题
            def safe_sql_string(value):
                if value is None:
                    return 'NULL'
                # 避免在 f-string 中使用反斜杠
                escaped_value = str(value).replace("'", "''")
                return f"'{escaped_value}'"

            # 确保日期类型正确
            voucher_date = form.voucher_date.data
            if isinstance(voucher_date, datetime):
                voucher_date = voucher_date.date()

            # 使用字符串格式化的SQL
            insert_sql = text(f"""
                INSERT INTO financial_vouchers
                (voucher_number, voucher_date, area_id, voucher_type, summary,
                 total_amount, status, source_type, source_id, attachment_count,
                 created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
                VALUES
                ({safe_sql_string(voucher_number)},
                 '{voucher_date}',
                 {user_area.id},
                 {safe_sql_string(form.voucher_type.data)},
                 {safe_sql_string(form.summary.data)},
                 0.00,
                 {safe_sql_string('草稿')},
                 {safe_sql_string('手工录入')},
                 NULL,
                 0,
                 {current_user.id},
                 NULL,
                 NULL,
                 NULL,
                 NULL,
                 {safe_sql_string(form.notes.data)})
            """)

            # 执行插入
            db.session.execute(insert_sql)

            # 获取刚插入的记录ID
            select_sql = text("""
                SELECT id FROM financial_vouchers
                WHERE voucher_number = :voucher_number AND area_id = :area_id
            """)
            result = db.session.execute(select_sql, {
                'voucher_number': voucher_number,
                'area_id': user_area.id
            })
            voucher_id = result.fetchone()[0]
            db.session.commit()
            
            flash('财务凭证创建成功', 'success')
            return redirect(url_for('financial.edit_voucher', id=voucher_id))
            
        except Exception as e:
            db.session.rollback()
            import traceback
            error_details = traceback.format_exc()
            current_app.logger.error(f"创建财务凭证失败: {str(e)}")
            current_app.logger.error(f"详细错误信息: {error_details}")
            flash(f'创建失败：{str(e)}', 'danger')
    
    return render_template('financial/vouchers/form.html', form=form)


@financial_bp.route('/vouchers/debug-create', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def debug_create_voucher(user_area):
    """调试凭证创建问题"""
    debug_info = {
        'user_id': current_user.id,
        'user_area_id': user_area.id,
        'user_area_name': user_area.name,
        'form_validation': None,
        'database_test': None,
        'error_details': None
    }

    if request.method == 'POST':
        form = FinancialVoucherForm()
        debug_info['form_validation'] = {
            'is_valid': form.validate_on_submit(),
            'errors': form.errors,
            'data': {
                'voucher_date': str(form.voucher_date.data) if form.voucher_date.data else None,
                'voucher_type': form.voucher_type.data,
                'summary': form.summary.data,
                'notes': form.notes.data
            }
        }

        if form.validate_on_submit():
            try:
                # 测试数据库连接和基本操作
                test_query = FinancialVoucher.query.filter_by(area_id=user_area.id).count()
                debug_info['database_test'] = f'现有凭证数量: {test_query}'

                # 尝试创建凭证（使用新的生成函数）
                voucher_number = generate_voucher_number(user_area, form.voucher_date.data)

                debug_info['voucher_number'] = voucher_number

                # 完全按照 .CursorRules 方案创建凭证对象
                from decimal import Decimal

                insert_sql = text("""
                    INSERT INTO financial_vouchers
                    (voucher_number, voucher_date, area_id, voucher_type, summary,
                     total_amount, status, source_type, source_id, attachment_count,
                     created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
                    VALUES
                    (:voucher_number, :voucher_date, :area_id, :voucher_type, :summary,
                     :total_amount, :status, :source_type, :source_id, :attachment_count,
                     :created_by, :reviewed_by, :reviewed_at, :posted_by, :posted_at, :notes)
                """)

                params = {
                    'voucher_number': voucher_number,
                    'voucher_date': form.voucher_date.data,
                    'area_id': user_area.id,
                    'voucher_type': form.voucher_type.data,
                    'summary': form.summary.data,
                    'total_amount': Decimal('0.00'),
                    'status': '草稿',
                    'source_type': '手工录入',
                    'source_id': None,
                    'attachment_count': 0,
                    'created_by': current_user.id,
                    'reviewed_by': None,
                    'reviewed_at': None,
                    'posted_by': None,
                    'posted_at': None,
                    'notes': form.notes.data
                }

                db.session.execute(insert_sql, params)
                db.session.commit()

                # 获取刚插入的记录ID
                select_sql = text("""
                    SELECT id FROM financial_vouchers
                    WHERE voucher_number = :voucher_number AND area_id = :area_id
                """)
                result = db.session.execute(select_sql, {
                    'voucher_number': voucher_number,
                    'area_id': user_area.id
                })
                voucher_id = result.fetchone()[0]
                db.session.commit()

                debug_info['success'] = True
                debug_info['voucher_id'] = voucher_id
                flash('调试：凭证创建成功！', 'success')

            except Exception as e:
                db.session.rollback()
                import traceback
                error_details = traceback.format_exc()
                debug_info['error_details'] = {
                    'error_message': str(e),
                    'traceback': error_details
                }
                flash(f'调试：创建失败 - {str(e)}', 'danger')
    else:
        form = FinancialVoucherForm()

    return render_template('financial/vouchers/debug.html',
                         form=form, debug_info=debug_info)


@financial_bp.route('/vouchers/simple-test', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def simple_test_create(user_area):
    """简化测试凭证创建"""
    if request.method == 'POST':
        try:
            # 最简单的凭证创建测试（使用新的生成函数）
            today = date.today()
            school_initials = get_school_pinyin_initials(user_area.name)
            voucher_number = f"{school_initials}TEST{today.strftime('%Y%m%d')}001"

            # 检查是否已存在相同凭证号
            existing = FinancialVoucher.query.filter_by(
                area_id=user_area.id,
                voucher_number=voucher_number
            ).first()

            if existing:
                # 如果存在，生成新的编号
                import random
                voucher_number = f"{school_initials}TEST{today.strftime('%Y%m%d')}{random.randint(100, 999)}"

            # 完全按照 .CursorRules 方案创建测试凭证
            from decimal import Decimal

            insert_sql = text("""
                INSERT INTO financial_vouchers
                (voucher_number, voucher_date, area_id, voucher_type, summary,
                 total_amount, status, source_type, source_id, attachment_count,
                 created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
                VALUES
                (:voucher_number, :voucher_date, :area_id, :voucher_type, :summary,
                 :total_amount, :status, :source_type, :source_id, :attachment_count,
                 :created_by, :reviewed_by, :reviewed_at, :posted_by, :posted_at, :notes)
            """)

            params = {
                'voucher_number': voucher_number,
                'voucher_date': today,
                'area_id': user_area.id,
                'voucher_type': '测试凭证',
                'summary': '简化测试凭证',
                'total_amount': Decimal('0.00'),
                'status': '草稿',
                'source_type': '手工录入',
                'source_id': None,
                'attachment_count': 0,
                'created_by': current_user.id,
                'reviewed_by': None,
                'reviewed_at': None,
                'posted_by': None,
                'posted_at': None,
                'notes': None
            }

            db.session.execute(insert_sql, params)
            db.session.commit()

            # 获取刚插入的记录ID
            select_sql = text("""
                SELECT id FROM financial_vouchers
                WHERE voucher_number = :voucher_number AND area_id = :area_id
            """)
            result = db.session.execute(select_sql, {
                'voucher_number': voucher_number,
                'area_id': user_area.id
            })
            voucher_id = result.fetchone()[0]
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '测试凭证创建成功',
                'voucher_id': voucher_id,
                'voucher_number': voucher_number
            })

        except Exception as e:
            db.session.rollback()
            import traceback
            return jsonify({
                'success': False,
                'message': str(e),
                'traceback': traceback.format_exc()
            })

    return render_template('financial/vouchers/simple_test.html', user_area=user_area)


@financial_bp.route('/vouchers/check-system')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def check_system_status(user_area):
    """检查系统状态"""
    status = {
        'database_connection': False,
        'user_permissions': False,
        'area_access': False,
        'voucher_table': False,
        'existing_vouchers': 0,
        'error_message': None
    }

    try:
        # 检查数据库连接
        db.session.execute(text('SELECT 1')).fetchone()
        status['database_connection'] = True

        # 检查用户权限
        if current_user.is_authenticated:
            status['user_permissions'] = True

        # 检查区域访问
        if user_area:
            status['area_access'] = True

        # 检查凭证表
        count = FinancialVoucher.query.filter_by(area_id=user_area.id).count()
        status['voucher_table'] = True
        status['existing_vouchers'] = count

    except Exception as e:
        status['error_message'] = str(e)

    return jsonify(status)


@financial_bp.route('/vouchers/<int:id>')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def view_voucher(id, user_area):
    """查看财务凭证"""
    voucher = FinancialVoucher.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 获取凭证明细
    details = VoucherDetail.query.filter_by(voucher_id=id).order_by(VoucherDetail.line_number).all()
    
    return render_template('financial/vouchers/view.html', 
                         voucher=voucher, details=details)


@financial_bp.route('/vouchers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def edit_voucher(id, user_area):
    """编辑财务凭证"""
    voucher = FinancialVoucher.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 已审核的凭证不允许编辑
    if voucher.status in ['已审核', '已记账']:
        flash('已审核的凭证不允许编辑', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))
    
    form = FinancialVoucherForm(obj=voucher)
    
    if form.validate_on_submit():
        try:
            # 使用 ORM 更新凭证
            voucher.voucher_date = form.voucher_date.data
            voucher.voucher_type = form.voucher_type.data
            voucher.summary = form.summary.data
            voucher.notes = form.notes.data

            db.session.commit()
            
            flash('财务凭证更新成功', 'success')
            return redirect(url_for('financial.view_voucher', id=id))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新财务凭证失败: {str(e)}")
            flash('更新失败，请重试', 'danger')
    
    # 获取凭证明细
    details = VoucherDetail.query.filter_by(voucher_id=id).order_by(VoucherDetail.line_number).all()
    
    return render_template('financial/vouchers/edit.html', 
                         form=form, voucher=voucher, details=details)


@financial_bp.route('/vouchers/<int:id>/delete', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'delete')
def delete_voucher(id, user_area):
    """删除财务凭证"""
    voucher = FinancialVoucher.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 已审核的凭证不允许删除
    if voucher.status in ['已审核', '已记账']:
        flash('已审核的凭证不允许删除', 'warning')
        return redirect(url_for('financial.vouchers_index'))
    
    try:
        # 使用 ORM 删除（级联删除明细）
        db.session.delete(voucher)
        db.session.commit()
        
        flash('财务凭证删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除财务凭证失败: {str(e)}")
        flash('删除失败，请重试', 'danger')
    
    return redirect(url_for('financial.vouchers_index'))


@financial_bp.route('/vouchers/<int:id>/review', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'review')
def review_voucher(id, user_area):
    """审核财务凭证"""
    voucher = FinancialVoucher.query.filter_by(
        id=id,
        area_id=user_area.id
    ).first_or_404()

    if voucher.status != '待审核':
        flash('只能审核待审核状态的凭证', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))

    # 检查凭证明细是否平衡
    details = VoucherDetail.query.filter_by(voucher_id=id).all()
    if not details:
        flash('凭证没有明细，无法审核', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))

    total_debit = sum(detail.debit_amount for detail in details)
    total_credit = sum(detail.credit_amount for detail in details)

    if abs(total_debit - total_credit) > 0.01:  # 允许0.01的误差
        flash('凭证借贷不平衡，无法审核', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))

    try:
        # 使用 ORM 更新审核状态
        voucher.status = '已审核'
        voucher.reviewed_by = current_user.id
        voucher.reviewed_at = datetime.now()

        db.session.commit()

        flash('凭证审核成功', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"审核凭证失败: {str(e)}")
        flash('审核失败，请重试', 'danger')

    return redirect(url_for('financial.view_voucher', id=id))


# ==================== 凭证明细管理 API ====================

@financial_bp.route('/vouchers/<int:voucher_id>/details', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def add_voucher_detail(voucher_id, user_area):
    """添加凭证明细"""
    voucher = FinancialVoucher.query.filter_by(
        id=voucher_id,
        area_id=user_area.id
    ).first_or_404()

    # 移除状态限制，允许所有状态的凭证添加明细
    # if voucher.status != '草稿':
    #     return jsonify({'success': False, 'message': '只有草稿状态的凭证才能添加明细'})

    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['subject_id', 'summary']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必填字段: {field}'})

        # 验证借贷金额（至少一个大于0）
        debit_amount = float(data.get('debit_amount', 0))
        credit_amount = float(data.get('credit_amount', 0))

        if debit_amount <= 0 and credit_amount <= 0:
            return jsonify({'success': False, 'message': '借方金额或贷方金额至少有一个大于0'})

        if debit_amount > 0 and credit_amount > 0:
            return jsonify({'success': False, 'message': '借方金额和贷方金额不能同时大于0'})

        # 验证会计科目是否存在（支持系统科目和学校科目）
        from app.models_financial import AccountingSubject
        from app.models import AdministrativeArea

        # 获取系统区域ID（最小的区域ID）
        system_area = AdministrativeArea.query.order_by(AdministrativeArea.id).first()
        system_area_id = system_area.id if system_area else 0

        subject = AccountingSubject.query.filter(
            AccountingSubject.id == data['subject_id'],
            db.or_(
                # 系统科目
                db.and_(
                    AccountingSubject.area_id == system_area_id,
                    AccountingSubject.is_system == True
                ),
                # 学校科目
                db.and_(
                    AccountingSubject.area_id == user_area.id,
                    AccountingSubject.is_system == False
                )
            ),
            AccountingSubject.is_active == True
        ).first()

        if not subject:
            return jsonify({'success': False, 'message': '会计科目不存在或已停用'})

        # 获取下一个行号
        max_line_number = db.session.query(
            func.max(VoucherDetail.line_number)
        ).filter_by(voucher_id=voucher_id).scalar() or 0

        # 使用原生SQL创建明细
        insert_sql = text("""
            INSERT INTO voucher_details
            (voucher_id, line_number, subject_id, summary, debit_amount, credit_amount, auxiliary_info)
            VALUES
            (:voucher_id, :line_number, :subject_id, :summary, :debit_amount, :credit_amount, :auxiliary_info)
        """)

        params = {
            'voucher_id': voucher_id,
            'line_number': max_line_number + 1,
            'subject_id': data['subject_id'],
            'summary': data['summary'],
            'debit_amount': debit_amount,
            'credit_amount': credit_amount,
            'auxiliary_info': data.get('auxiliary_info', '')
        }

        db.session.execute(insert_sql, params)

        # 更新凭证总金额
        total_debit = db.session.query(
            func.sum(VoucherDetail.debit_amount)
        ).filter_by(voucher_id=voucher_id).scalar() or 0

        voucher.total_amount = float(total_debit)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '明细添加成功',
            'detail': {
                'line_number': max_line_number + 1,
                'subject_name': f"{subject.code} - {subject.name}",
                'summary': data['summary'],
                'debit_amount': debit_amount,
                'credit_amount': credit_amount
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加凭证明细失败: {str(e)}")
        return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})


@financial_bp.route('/vouchers/<int:voucher_id>/details/<int:detail_id>', methods=['PUT'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def update_voucher_detail(voucher_id, detail_id, user_area):
    """更新凭证明细"""
    voucher = FinancialVoucher.query.filter_by(
        id=voucher_id,
        area_id=user_area.id
    ).first_or_404()

    # 移除状态限制，允许所有状态的凭证修改明细
    # if voucher.status != '草稿':
    #     return jsonify({'success': False, 'message': '只有草稿状态的凭证才能修改明细'})

    detail = VoucherDetail.query.filter_by(
        id=detail_id,
        voucher_id=voucher_id
    ).first_or_404()

    try:
        data = request.get_json()

        # 验证借贷金额
        debit_amount = float(data.get('debit_amount', 0))
        credit_amount = float(data.get('credit_amount', 0))

        if debit_amount <= 0 and credit_amount <= 0:
            return jsonify({'success': False, 'message': '借方金额或贷方金额至少有一个大于0'})

        if debit_amount > 0 and credit_amount > 0:
            return jsonify({'success': False, 'message': '借方金额和贷方金额不能同时大于0'})

        # 验证会计科目（支持系统科目和学校科目）
        if 'subject_id' in data:
            from app.models_financial import AccountingSubject
            from app.models import AdministrativeArea

            # 获取系统区域ID（最小的区域ID）
            system_area = AdministrativeArea.query.order_by(AdministrativeArea.id).first()
            system_area_id = system_area.id if system_area else 0

            subject = AccountingSubject.query.filter(
                AccountingSubject.id == data['subject_id'],
                db.or_(
                    # 系统科目
                    db.and_(
                        AccountingSubject.area_id == system_area_id,
                        AccountingSubject.is_system == True
                    ),
                    # 学校科目
                    db.and_(
                        AccountingSubject.area_id == user_area.id,
                        AccountingSubject.is_system == False
                    )
                ),
                AccountingSubject.is_active == True
            ).first()

            if not subject:
                return jsonify({'success': False, 'message': '会计科目不存在或已停用'})

        # 使用原生SQL更新明细
        update_sql = text("""
            UPDATE voucher_details
            SET subject_id = :subject_id,
                summary = :summary,
                debit_amount = :debit_amount,
                credit_amount = :credit_amount,
                auxiliary_info = :auxiliary_info
            WHERE id = :detail_id
        """)

        params = {
            'detail_id': detail_id,
            'subject_id': data.get('subject_id', detail.subject_id),
            'summary': data.get('summary', detail.summary),
            'debit_amount': debit_amount,
            'credit_amount': credit_amount,
            'auxiliary_info': data.get('auxiliary_info', detail.auxiliary_info or '')
        }

        db.session.execute(update_sql, params)

        # 更新凭证总金额
        total_debit = db.session.query(
            func.sum(VoucherDetail.debit_amount)
        ).filter_by(voucher_id=voucher_id).scalar() or 0

        voucher.total_amount = float(total_debit)

        db.session.commit()

        return jsonify({'success': True, 'message': '明细更新成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新凭证明细失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})


@financial_bp.route('/vouchers/<int:voucher_id>/details/<int:detail_id>', methods=['GET'])
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def get_voucher_detail(voucher_id, detail_id, user_area):
    """获取凭证明细信息"""
    try:
        # 验证凭证是否存在且属于当前学校
        voucher = FinancialVoucher.query.filter_by(
            id=voucher_id,
            area_id=user_area.id
        ).first_or_404()

        # 获取明细
        detail = VoucherDetail.query.filter_by(
            id=detail_id,
            voucher_id=voucher_id
        ).first()

        if not detail:
            return jsonify({'success': False, 'message': '明细不存在'})

        return jsonify({
            'success': True,
            'detail': {
                'id': detail.id,
                'subject_id': detail.subject_id,
                'summary': detail.summary,
                'debit_amount': float(detail.debit_amount),
                'credit_amount': float(detail.credit_amount),
                'auxiliary_info': detail.auxiliary_info
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取凭证明细失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取失败: {str(e)}'})


@financial_bp.route('/vouchers/<int:voucher_id>/details/<int:detail_id>', methods=['DELETE'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def delete_voucher_detail(voucher_id, detail_id, user_area):
    """删除凭证明细"""
    voucher = FinancialVoucher.query.filter_by(
        id=voucher_id,
        area_id=user_area.id
    ).first_or_404()

    # 移除状态限制，允许所有状态的凭证删除明细
    # if voucher.status != '草稿':
    #     return jsonify({'success': False, 'message': '只有草稿状态的凭证才能删除明细'})

    detail = VoucherDetail.query.filter_by(
        id=detail_id,
        voucher_id=voucher_id
    ).first_or_404()

    try:
        # 使用原生SQL删除明细
        delete_sql = text("DELETE FROM voucher_details WHERE id = :detail_id")
        db.session.execute(delete_sql, {'detail_id': detail_id})

        # 重新排序行号
        reorder_sql = text("""
            UPDATE voucher_details
            SET line_number = new_line_number
            FROM (
                SELECT id, ROW_NUMBER() OVER (ORDER BY line_number) as new_line_number
                FROM voucher_details
                WHERE voucher_id = :voucher_id
            ) as numbered
            WHERE voucher_details.id = numbered.id
        """)
        db.session.execute(reorder_sql, {'voucher_id': voucher_id})

        # 更新凭证总金额
        total_debit = db.session.query(
            func.sum(VoucherDetail.debit_amount)
        ).filter_by(voucher_id=voucher_id).scalar() or 0

        voucher.total_amount = float(total_debit)

        db.session.commit()

        return jsonify({'success': True, 'message': '明细删除成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除凭证明细失败: {str(e)}")
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})


@financial_bp.route('/vouchers/<int:voucher_id>/submit-review', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def submit_voucher_for_review(voucher_id, user_area):
    """提交凭证审核"""
    voucher = FinancialVoucher.query.filter_by(
        id=voucher_id,
        area_id=user_area.id
    ).first_or_404()

    # 只有草稿状态的凭证才能提交审核
    if voucher.status != '草稿':
        return jsonify({'success': False, 'message': '只有草稿状态的凭证才能提交审核'})

    # 检查是否有明细
    details = VoucherDetail.query.filter_by(voucher_id=voucher_id).all()
    if not details:
        return jsonify({'success': False, 'message': '凭证没有明细，无法提交审核'})

    # 检查借贷是否平衡
    total_debit = sum(float(detail.debit_amount) for detail in details)
    total_credit = sum(float(detail.credit_amount) for detail in details)

    if abs(total_debit - total_credit) > 0.01:  # 允许0.01的误差
        return jsonify({
            'success': False,
            'message': f'凭证借贷不平衡，无法提交审核。借方合计: {total_debit:.2f}，贷方合计: {total_credit:.2f}'
        })

    try:
        # 更新凭证状态
        voucher.status = '待审核'
        db.session.commit()

        return jsonify({'success': True, 'message': '凭证已提交审核'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"提交凭证审核失败: {str(e)}")
        return jsonify({'success': False, 'message': f'提交失败: {str(e)}'})


# ==================== 调试路由 ====================

@financial_bp.route('/debug/user-info')
@login_required
@school_required
def debug_user_info(user_area):
    """调试用户信息"""
    from app.models_financial import AccountingSubject

    # 获取当前用户信息
    user_info = {
        'user_id': current_user.id,
        'username': current_user.username,
        'area_id': user_area.id,
        'area_name': user_area.name
    }

    # 获取该区域的会计科目数量
    subjects_count = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        is_active=True
    ).count()

    # 获取前5个会计科目
    subjects = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        is_active=True
    ).limit(5).all()

    subjects_list = []
    for subject in subjects:
        subjects_list.append({
            'id': subject.id,
            'code': subject.code,
            'name': subject.name,
            'area_id': subject.area_id
        })

    return jsonify({
        'user_info': user_info,
        'subjects_count': subjects_count,
        'sample_subjects': subjects_list
    })


@financial_bp.route('/debug/copy-subjects', methods=['POST'])
@login_required
@school_required
def copy_subjects_to_current_area(user_area):
    """为当前区域复制会计科目"""
    from app.models_financial import AccountingSubject

    try:
        # 检查当前区域是否已有会计科目
        existing_count = AccountingSubject.query.filter_by(area_id=user_area.id).count()

        if existing_count > 0:
            return jsonify({
                'success': False,
                'message': f'当前区域已有 {existing_count} 个会计科目，无需复制'
            })

        # 从区域ID=7复制会计科目（这个区域有完整的科目数据）
        source_subjects = AccountingSubject.query.filter_by(area_id=7, is_active=True).all()

        if not source_subjects:
            return jsonify({
                'success': False,
                'message': '没有找到源会计科目数据'
            })

        # 批量复制会计科目
        copied_count = 0
        for source_subject in source_subjects:
            # 使用原生SQL插入
            sql = text("""
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (:code, :name, :parent_id, :level, :subject_type, :balance_direction,
                 :area_id, :is_system, :is_active, :description, :created_by)
            """)

            params = {
                'code': source_subject.code,
                'name': source_subject.name,
                'parent_id': source_subject.parent_id,
                'level': source_subject.level,
                'subject_type': source_subject.subject_type,
                'balance_direction': source_subject.balance_direction,
                'area_id': user_area.id,  # 使用当前用户的区域ID
                'is_system': source_subject.is_system,
                'is_active': True,
                'description': source_subject.description,
                'created_by': current_user.id
            }

            db.session.execute(sql, params)
            copied_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功为区域 {user_area.name} 复制了 {copied_count} 个会计科目'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"复制会计科目失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'复制失败: {str(e)}'
        })


@financial_bp.route('/vouchers/pending-stock-ins')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def pending_stock_ins_for_voucher(user_area):
    """待生成凭证的入库单列表"""

    # 查询已财务确认但未生成凭证的入库单
    from app.models import StockIn
    stock_ins = StockIn.query.filter(
        StockIn.area_id == user_area.id,
        StockIn.is_financial_confirmed == True,
        StockIn.voucher_id.is_(None),
        StockIn.total_cost > 0
    ).order_by(StockIn.created_at.desc()).all()

    return render_template('financial/vouchers/pending_stock_ins.html',
                         stock_ins=stock_ins, user_area=user_area)


@financial_bp.route('/vouchers/generate-from-stock-in', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def generate_voucher_from_stock_in(user_area):
    """从单个入库单生成财务凭证"""
    stock_in_id = request.json.get('stock_in_id')

    if not stock_in_id:
        return jsonify({'success': False, 'message': '请选择入库单'})

    # 检查入库单是否存在且属于当前学校
    from app.models import StockIn
    stock_in = StockIn.query.filter_by(
        id=stock_in_id,
        area_id=user_area.id
    ).first()

    if not stock_in:
        return jsonify({'success': False, 'message': '入库单不存在'})

    # 检查是否已生成凭证
    if stock_in.voucher_id:
        return jsonify({'success': False, 'message': '该入库单已生成财务凭证'})

    # 检查是否财务确认
    if not stock_in.is_financial_confirmed:
        return jsonify({'success': False, 'message': '入库单未财务确认，无法生成凭证'})

    try:
        result = create_voucher_from_stock_in(stock_in, user_area)
        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"从入库单生成凭证失败: {str(e)}")
        return jsonify({'success': False, 'message': '生成失败，请重试'})


@financial_bp.route('/vouchers/batch-generate-from-stock-ins', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def batch_generate_vouchers_from_stock_ins(user_area):
    """批量从入库单生成财务凭证"""
    start_date = request.json.get('start_date')
    end_date = request.json.get('end_date')
    auto_review = request.json.get('auto_review', True)

    if not start_date or not end_date:
        return jsonify({'success': False, 'message': '请选择日期范围'})

    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'success': False, 'message': '日期格式错误'})

    try:
        # 查询指定日期范围内已财务确认但未生成凭证的入库单
        from app.models import StockIn
        stock_ins = StockIn.query.filter(
            StockIn.area_id == user_area.id,
            StockIn.is_financial_confirmed == True,
            StockIn.voucher_id.is_(None),
            StockIn.total_cost > 0,
            StockIn.created_at >= start_date_obj,
            StockIn.created_at <= end_date_obj
        ).all()

        if not stock_ins:
            return jsonify({
                'success': True,
                'message': '指定日期范围内没有待生成凭证的入库单',
                'success_count': 0,
                'failed_count': 0,
                'total_amount': 0
            })

        success_count = 0
        failed_count = 0
        total_amount = 0

        for stock_in in stock_ins:
            try:
                result = create_voucher_from_stock_in(stock_in, user_area, auto_review)
                if result['success']:
                    success_count += 1
                    total_amount += float(stock_in.total_cost)
                else:
                    failed_count += 1
                    current_app.logger.warning(f"入库单 {stock_in.stock_in_number} 生成凭证失败: {result['message']}")
            except Exception as e:
                failed_count += 1
                current_app.logger.error(f"入库单 {stock_in.stock_in_number} 生成凭证异常: {str(e)}")

        return jsonify({
            'success': True,
            'message': f'批量生成完成，成功 {success_count} 个，失败 {failed_count} 个',
            'success_count': success_count,
            'failed_count': failed_count,
            'total_amount': total_amount
        })

    except Exception as e:
        current_app.logger.error(f"批量生成凭证失败: {str(e)}")
        return jsonify({'success': False, 'message': '批量生成失败，请重试'})


def create_voucher_from_stock_in(stock_in, user_area, auto_review=True):
    """从入库单创建财务凭证的核心逻辑"""
    try:
        # 生成凭证号（使用新的生成函数）
        voucher_number = generate_voucher_number(user_area, date.today())

        # 使用原生SQL创建财务凭证，使用命名参数
        insert_sql = text("""
            INSERT INTO financial_vouchers
            (voucher_number, voucher_date, area_id, voucher_type, summary,
             total_amount, status, source_type, source_id, attachment_count,
             created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
            VALUES
            (:voucher_number, :voucher_date, :area_id, :voucher_type, :summary,
             :total_amount, :status, :source_type, :source_id, :attachment_count,
             :created_by, :reviewed_by, :reviewed_at, :posted_by, :posted_at, :notes)
        """)

        # 确保日期类型正确
        voucher_date = stock_in.stock_in_date
        if isinstance(voucher_date, datetime):
            voucher_date = voucher_date.date()

        # 确保时间戳类型正确
        reviewed_at = None
        if auto_review:
            reviewed_at = datetime.now().replace(microsecond=0)

        params = {
            'voucher_number': voucher_number,
            'voucher_date': voucher_date,
            'area_id': user_area.id,
            'voucher_type': '入库凭证',
            'summary': f'入库单{stock_in.stock_in_number}',
            'total_amount': Decimal(str(stock_in.total_cost)),
            'status': '已审核' if auto_review else '待审核',
            'source_type': '入库单',
            'source_id': stock_in.id,
            'attachment_count': 0,
            'created_by': stock_in.operator_id,
            'reviewed_by': stock_in.operator_id if auto_review else None,
            'reviewed_at': reviewed_at,
            'posted_by': None,
            'posted_at': None,
            'notes': f'自动生成自入库单{stock_in.stock_in_number}'
        }

        # 执行插入
        db.session.execute(insert_sql, params)

        # 获取刚插入的记录ID
        select_sql = text("""
            SELECT id FROM financial_vouchers
            WHERE voucher_number = :voucher_number AND area_id = :area_id
        """)
        result = db.session.execute(select_sql, {
            'voucher_number': voucher_number,
            'area_id': user_area.id
        })
        voucher_id = result.fetchone()[0]

        # 获取会计科目
        inventory_subject = AccountingSubject.query.filter_by(
            area_id=user_area.id,
            code='1402'  # 原材料
        ).first()

        payable_subject = AccountingSubject.query.filter_by(
            area_id=user_area.id,
            code='2201'  # 应付账款
        ).first()

        if not inventory_subject:
            db.session.rollback()
            return {'success': False, 'message': '未找到原材料科目(1402)，请先设置会计科目'}

        if not payable_subject:
            db.session.rollback()
            return {'success': False, 'message': '未找到应付账款科目(2201)，请先设置会计科目'}

        # 使用原生SQL生成凭证明细
        detail_sql = text("""
            INSERT INTO voucher_details
            (voucher_id, line_number, subject_id, summary, debit_amount, credit_amount)
            VALUES
            (:voucher_id, :line_number, :subject_id, :summary, :debit_amount, :credit_amount)
        """)

        # 借：原材料
        detail1_params = {
            'voucher_id': voucher_id,
            'line_number': 1,
            'subject_id': inventory_subject.id,
            'summary': f'入库单{stock_in.stock_in_number}',
            'debit_amount': Decimal(str(stock_in.total_cost)),
            'credit_amount': Decimal('0.00')
        }
        db.session.execute(detail_sql, detail1_params)

        # 贷：应付账款
        detail2_params = {
            'voucher_id': voucher_id,
            'line_number': 2,
            'subject_id': payable_subject.id,
            'summary': f'入库单{stock_in.stock_in_number}',
            'debit_amount': Decimal('0.00'),
            'credit_amount': Decimal(str(stock_in.total_cost))
        }
        db.session.execute(detail_sql, detail2_params)

        # 更新入库单关联信息
        stock_in.voucher_id = voucher_id

        db.session.commit()

        return {
            'success': True,
            'message': '财务凭证生成成功',
            'voucher_id': voucher_id,
            'voucher_number': voucher_number
        }

    except Exception as e:
        db.session.rollback()
        raise e
