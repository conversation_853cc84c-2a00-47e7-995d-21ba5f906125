{% extends "financial/base.html" %}

{% block page_title %}编辑财务凭证{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ url_for('financial.vouchers_index') }}">财务凭证管理</a></li>
<li class="breadcrumb-item active">编辑凭证</li>
{% endblock %}

{% block page_actions %}
<div class="financial-actions">
    <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="btn btn-info">
        <i class="fas fa-eye"></i> 查看详情
    </a>
    <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
    </a>
</div>
{% endblock %}

{% block financial_content %}
<div class="row">
    <div class="col-lg-12">
        <!-- 凭证基本信息 -->
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-edit"></i> 凭证基本信息
                <span class="badge badge-{{ 'warning' if voucher.status == '草稿' else 'info' }} ml-2">
                    {{ voucher.status }}
                </span>
            </div>
            <div class="financial-card-body">
                {% if form.errors %}
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> 表单验证失败</h6>
                    <ul class="mb-0">
                        {% for field_name, errors in form.errors.items() %}
                            {% for error in errors %}
                            <li><strong>{{ field_name }}:</strong> {{ error }}</li>
                            {% endfor %}
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}

                <form method="POST" class="financial-form" id="voucherForm">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">凭证号</label>
                                <input type="text" class="form-control" value="{{ voucher.voucher_number }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.voucher_date.label(class="form-label") }}
                                {{ form.voucher_date(class="form-control" + (" is-invalid" if form.voucher_date.errors else "")) }}
                                {% if form.voucher_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.voucher_date.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.voucher_type.label(class="form-label") }}
                                {{ form.voucher_type(class="form-control" + (" is-invalid" if form.voucher_type.errors else "")) }}
                                {% if form.voucher_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.voucher_type.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">凭证金额</label>
                                <input type="text" class="form-control financial-amount"
                                       value="{{ voucher.total_amount }}" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.summary.label(class="form-label") }}
                                {{ form.summary(class="form-control" + (" is-invalid" if form.summary.errors else "")) }}
                                {% if form.summary.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.summary.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    <span id="summaryCounter">0/200 字符</span>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="3") }}
                                {% if form.notes.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.notes.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save"></i> 更新凭证
                        </button>
                        <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 凭证明细 -->
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-list"></i> 凭证明细
                <div class="float-right">
                    {% if voucher.status == '草稿' %}
                    <button type="button" class="btn btn-success btn-sm" id="addDetailBtn">
                        <i class="fas fa-plus"></i> 添加明细
                    </button>
                    {% endif %}
                </div>
            </div>
            <div class="financial-card-body">
                <div class="table-responsive">
                    <table class="table financial-table" id="detailsTable">
                        <thead>
                            <tr>
                                <th width="8%">行号</th>
                                <th width="25%">会计科目</th>
                                <th width="25%">摘要</th>
                                <th width="12%">借方金额</th>
                                <th width="12%">贷方金额</th>
                                <th width="10%">辅助信息</th>
                                {% if voucher.status == '草稿' %}
                                <th width="8%">操作</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for detail in details %}
                            <tr data-detail-id="{{ detail.id }}">
                                <td>{{ detail.line_number }}</td>
                                <td>{{ detail.subject.code }} - {{ detail.subject.name }}</td>
                                <td>{{ detail.summary }}</td>
                                <td class="text-right">
                                    {% if detail.debit_amount > 0 %}
                                        <span class="financial-amount">{{ "%.2f"|format(detail.debit_amount) }}</span>
                                    {% endif %}
                                </td>
                                <td class="text-right">
                                    {% if detail.credit_amount > 0 %}
                                        <span class="financial-amount">{{ "%.2f"|format(detail.credit_amount) }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ detail.auxiliary_info or '' }}</td>
                                {% if voucher.status == '草稿' %}
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-warning btn-sm edit-detail-btn"
                                                data-detail-id="{{ detail.id }}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm delete-detail-btn"
                                                data-detail-id="{{ detail.id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                            {% if not details %}
                            <tr id="noDetailsRow">
                                <td colspan="{% if voucher.status == '草稿' %}7{% else %}6{% endif %}" class="text-center text-muted">
                                    暂无明细记录
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                        <tfoot>
                            <tr class="table-info">
                                <th colspan="3">合计</th>
                                <th class="text-right" id="totalDebit">0.00</th>
                                <th class="text-right" id="totalCredit">0.00</th>
                                <th colspan="{% if voucher.status == '草稿' %}2{% else %}1{% endif %}"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑明细模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">添加凭证明细</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="detailForm">
                    <!-- 会计科目分级选择 -->
                    <div class="form-group">
                        <label class="form-label">会计科目选择</label>

                        <!-- 步骤指示器 -->
                        <div class="subject-selection-steps mb-3">
                            <div class="step-indicator">
                                <div class="step active" id="step1">
                                    <span class="step-number">1</span>
                                    <span class="step-label">科目类型</span>
                                </div>
                                <div class="step" id="step2">
                                    <span class="step-number">2</span>
                                    <span class="step-label">一级科目</span>
                                </div>
                                <div class="step" id="step3">
                                    <span class="step-number">3</span>
                                    <span class="step-label">明细科目</span>
                                </div>
                            </div>
                        </div>

                        <!-- 第一步：科目类型选择 -->
                        <div class="selection-step" id="typeSelection">
                            <label class="form-label">第一步：选择科目类型</label>
                            <select class="form-control" id="subjectType" name="subject_type">
                                <option value="">请选择科目类型...</option>
                            </select>
                        </div>

                        <!-- 第二步：一级科目选择 -->
                        <div class="selection-step" id="parentSelection" style="display: none;">
                            <label class="form-label">第二步：选择一级科目</label>
                            <select class="form-control" id="parentSubject" name="parent_subject" disabled>
                                <option value="">请先选择科目类型...</option>
                            </select>
                        </div>

                        <!-- 第三步：明细科目选择 -->
                        <div class="selection-step" id="subjectSelection" style="display: none;">
                            <label class="form-label">第三步：选择明细科目</label>
                            <select class="form-control" id="finalSubject" name="subject_id" disabled>
                                <option value="">请先选择一级科目...</option>
                            </select>
                        </div>

                        <!-- 选择完成提示 -->
                        <div class="selection-complete" id="selectionComplete" style="display: none;">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                已选择科目：<span id="selectedSubjectDisplay"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="form-label">摘要 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="detailSummary" name="summary"
                                       placeholder="请输入摘要" maxlength="200" required>
                                <small class="form-text text-muted">
                                    <span id="detailSummaryCounter">0/200 字符</span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">借方金额</label>
                                <input type="number" class="form-control" id="debitAmount" name="debit_amount"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">贷方金额</label>
                                <input type="number" class="form-control" id="creditAmount" name="credit_amount"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">辅助信息</label>
                        <input type="text" class="form-control" id="auxiliaryInfo" name="auxiliary_info"
                               placeholder="可选，用于补充说明" maxlength="200">
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>提示：</strong>借方金额和贷方金额必须填写其中一个，且不能同时填写。
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveDetailBtn">
                    <i class="fas fa-save"></i> 保存明细
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<style>
/* 会计科目选择步骤样式 */
.subject-selection-steps {
    margin-bottom: 1.5rem;
}

.step-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.step-indicator::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e3e6f0;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background-color: white;
    padding: 0 10px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e3e6f0;
    color: #858796;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.step-label {
    font-size: 0.875rem;
    color: #858796;
    font-weight: 500;
}

.step.active .step-number {
    background-color: #4e73df;
    color: white;
}

.step.active .step-label {
    color: #4e73df;
    font-weight: 600;
}

.step.completed .step-number {
    background-color: #1cc88a;
    color: white;
}

.step.completed .step-label {
    color: #1cc88a;
}

.selection-step {
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    background-color: #f8f9fc;
}

.selection-complete {
    margin-top: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .step-indicator {
        flex-direction: column;
        gap: 1rem;
    }

    .step-indicator::before {
        display: none;
    }

    .step {
        flex-direction: row;
        gap: 0.5rem;
    }

    .step-number {
        margin-bottom: 0;
    }
}
</style>

<script>
// 全局变量
let currentEditingDetailId = null;
let subjectSelectionState = {
    step: 1,
    selectedType: null,
    selectedParent: null,
    selectedSubject: null
};

// 初始化页面
function initVoucherEditPage() {
    // 初始化摘要字符计数
    initSummaryCounter();

    // 初始化明细表格合计
    updateDetailTotals();

    // 初始化表单验证
    initFormValidation();

    // 加载科目类型
    loadSubjectTypes();

    // 绑定事件
    bindEvents();
}

// 初始化摘要字符计数
function initSummaryCounter() {
    const summaryField = document.querySelector('input[name="summary"]');
    const counter = document.getElementById('summaryCounter');

    if (summaryField && counter) {
        function updateCounter() {
            const length = summaryField.value.length;
            counter.textContent = `${length}/200 字符`;
            if (length > 200) {
                counter.className = 'form-text text-danger';
            } else if (length > 180) {
                counter.className = 'form-text text-warning';
            } else {
                counter.className = 'form-text text-muted';
            }
        }

        summaryField.addEventListener('input', updateCounter);
        updateCounter();
    }

    // 明细摘要字符计数
    const detailSummaryField = document.getElementById('detailSummary');
    const detailCounter = document.getElementById('detailSummaryCounter');

    if (detailSummaryField && detailCounter) {
        function updateDetailCounter() {
            const length = detailSummaryField.value.length;
            detailCounter.textContent = `${length}/200 字符`;
            if (length > 200) {
                detailCounter.className = 'form-text text-danger';
            } else if (length > 180) {
                detailCounter.className = 'form-text text-warning';
            } else {
                detailCounter.className = 'form-text text-muted';
            }
        }

        detailSummaryField.addEventListener('input', updateDetailCounter);
        updateDetailCounter();
    }
}

// 更新明细合计
function updateDetailTotals() {
    let totalDebit = 0;
    let totalCredit = 0;

    document.querySelectorAll('#detailsTable tbody tr[data-detail-id]').forEach(row => {
        const debitCell = row.querySelector('td:nth-child(4) .financial-amount');
        const creditCell = row.querySelector('td:nth-child(5) .financial-amount');

        if (debitCell) {
            totalDebit += parseFloat(debitCell.textContent) || 0;
        }
        if (creditCell) {
            totalCredit += parseFloat(creditCell.textContent) || 0;
        }
    });

    document.getElementById('totalDebit').textContent = totalDebit.toFixed(2);
    document.getElementById('totalCredit').textContent = totalCredit.toFixed(2);
}

// 初始化表单验证
function initFormValidation() {
    const voucherForm = document.getElementById('voucherForm');
    if (voucherForm) {
        voucherForm.addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;

            // 显示提交状态
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 更新中...';
            submitBtn.disabled = true;

            // 基本验证
            const voucherDate = document.querySelector('input[name="voucher_date"]').value;
            const voucherType = document.querySelector('select[name="voucher_type"]').value;
            const summary = document.querySelector('input[name="summary"]').value;

            if (!voucherDate || !voucherType || !summary.trim()) {
                e.preventDefault();
                alert('请填写所有必填字段：凭证日期、凭证类型、摘要');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                return false;
            }

            if (summary.length > 200) {
                e.preventDefault();
                alert('摘要长度不能超过200个字符');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                return false;
            }
        });
    }
}

// 绑定事件
function bindEvents() {
    // 科目类型选择事件
    document.getElementById('subjectType').addEventListener('change', function() {
        const selectedType = this.value;
        if (selectedType) {
            subjectSelectionState.selectedType = selectedType;
            subjectSelectionState.step = 2;
            updateStepIndicator();
            loadParentSubjects(selectedType);
        } else {
            resetSubjectSelection();
        }
    });

    // 一级科目选择事件
    document.getElementById('parentSubject').addEventListener('change', function() {
        const selectedParent = this.value;
        if (selectedParent) {
            subjectSelectionState.selectedParent = selectedParent;
            subjectSelectionState.step = 3;
            updateStepIndicator();
            loadChildSubjects(selectedParent);
        } else {
            // 重置到第二步
            subjectSelectionState.step = 2;
            subjectSelectionState.selectedParent = null;
            subjectSelectionState.selectedSubject = null;
            updateStepIndicator();
            resetFinalSubjectSelection();
        }
    });

    // 明细科目选择事件
    document.getElementById('finalSubject').addEventListener('change', function() {
        const selectedSubject = this.value;
        if (selectedSubject) {
            subjectSelectionState.selectedSubject = selectedSubject;
            showSelectionComplete();
        } else {
            hideSelectionComplete();
        }
    });

    // 保存明细按钮事件
    document.getElementById('saveDetailBtn').addEventListener('click', saveDetail);

    // 添加明细按钮事件
    const addDetailBtn = document.getElementById('addDetailBtn');
    if (addDetailBtn) {
        addDetailBtn.addEventListener('click', showAddDetailModal);
    }

    // 编辑明细按钮事件（使用事件委托）
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-detail-btn')) {
            const detailId = e.target.closest('.edit-detail-btn').dataset.detailId;
            editDetail(parseInt(detailId));
        }
    });

    // 删除明细按钮事件（使用事件委托）
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-detail-btn')) {
            const detailId = e.target.closest('.delete-detail-btn').dataset.detailId;
            deleteDetail(parseInt(detailId));
        }
    });

    // 借贷金额互斥验证
    document.getElementById('debitAmount').addEventListener('input', function() {
        if (this.value && parseFloat(this.value) > 0) {
            document.getElementById('creditAmount').value = '';
        }
    });

    document.getElementById('creditAmount').addEventListener('input', function() {
        if (this.value && parseFloat(this.value) > 0) {
            document.getElementById('debitAmount').value = '';
        }
    });
}

// 加载科目类型
function loadSubjectTypes() {
    fetch('{{ url_for("financial.accounting_subject_types_api") }}')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('subjectType');
            select.innerHTML = '<option value="">请选择科目类型...</option>';

            data.forEach(type => {
                const option = document.createElement('option');
                option.value = type.value;
                option.textContent = type.label;
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('加载科目类型失败:', error);
            alert('加载科目类型失败，请刷新页面重试');
        });
}

// 加载一级科目
function loadParentSubjects(subjectType) {
    const parentSelect = document.getElementById('parentSubject');
    parentSelect.innerHTML = '<option value="">加载中...</option>';
    parentSelect.disabled = true;

    fetch(`{{ url_for("financial.accounting_subjects_by_type") }}?subject_type=${encodeURIComponent(subjectType)}`)
        .then(response => response.json())
        .then(data => {
            parentSelect.innerHTML = '<option value="">请选择一级科目...</option>';

            data.forEach(subject => {
                const option = document.createElement('option');
                option.value = subject.id;
                option.textContent = subject.display_name;
                option.dataset.code = subject.code;
                option.dataset.name = subject.name;
                parentSelect.appendChild(option);
            });

            parentSelect.disabled = false;
            document.getElementById('parentSelection').style.display = 'block';
        })
        .catch(error => {
            console.error('加载一级科目失败:', error);
            parentSelect.innerHTML = '<option value="">加载失败，请重试</option>';
            parentSelect.disabled = false;
        });
}

// 加载明细科目
function loadChildSubjects(parentId) {
    const finalSelect = document.getElementById('finalSubject');
    finalSelect.innerHTML = '<option value="">加载中...</option>';
    finalSelect.disabled = true;

    fetch(`{{ url_for("financial.accounting_subjects_by_parent") }}?parent_id=${parentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.length === 0) {
                // 没有下级科目，直接使用上级科目
                const parentSelect = document.getElementById('parentSubject');
                const selectedOption = parentSelect.options[parentSelect.selectedIndex];

                finalSelect.innerHTML = `<option value="${parentId}" selected>${selectedOption.textContent}</option>`;
                finalSelect.disabled = false;

                // 自动选择并完成
                subjectSelectionState.selectedSubject = parentId;
                showSelectionComplete();
            } else {
                // 有下级科目，显示选择列表
                finalSelect.innerHTML = '<option value="">请选择明细科目...</option>';

                data.forEach(subject => {
                    const option = document.createElement('option');
                    option.value = subject.id;
                    option.textContent = subject.display_name;
                    option.dataset.code = subject.code;
                    option.dataset.name = subject.name;
                    finalSelect.appendChild(option);
                });

                finalSelect.disabled = false;
            }

            document.getElementById('subjectSelection').style.display = 'block';
        })
        .catch(error => {
            console.error('加载明细科目失败:', error);
            finalSelect.innerHTML = '<option value="">加载失败，请重试</option>';
            finalSelect.disabled = false;
        });
}

// 更新步骤指示器
function updateStepIndicator() {
    // 重置所有步骤
    document.querySelectorAll('.step').forEach(step => {
        step.classList.remove('active', 'completed');
    });

    // 设置已完成的步骤
    for (let i = 1; i < subjectSelectionState.step; i++) {
        document.getElementById(`step${i}`).classList.add('completed');
    }

    // 设置当前步骤
    if (subjectSelectionState.step <= 3) {
        document.getElementById(`step${subjectSelectionState.step}`).classList.add('active');
    }
}

// 重置科目选择
function resetSubjectSelection() {
    subjectSelectionState = {
        step: 1,
        selectedType: null,
        selectedParent: null,
        selectedSubject: null
    };

    updateStepIndicator();

    // 隐藏后续选择步骤
    document.getElementById('parentSelection').style.display = 'none';
    document.getElementById('subjectSelection').style.display = 'none';
    document.getElementById('selectionComplete').style.display = 'none';

    // 重置选择框
    document.getElementById('parentSubject').innerHTML = '<option value="">请先选择科目类型...</option>';
    document.getElementById('parentSubject').disabled = true;
    resetFinalSubjectSelection();
}

// 重置明细科目选择
function resetFinalSubjectSelection() {
    document.getElementById('finalSubject').innerHTML = '<option value="">请先选择一级科目...</option>';
    document.getElementById('finalSubject').disabled = true;
    document.getElementById('subjectSelection').style.display = 'none';
    hideSelectionComplete();
}

// 显示选择完成状态
function showSelectionComplete() {
    const finalSelect = document.getElementById('finalSubject');
    const selectedOption = finalSelect.options[finalSelect.selectedIndex];

    if (selectedOption && selectedOption.value) {
        document.getElementById('selectedSubjectDisplay').textContent = selectedOption.textContent;
        document.getElementById('selectionComplete').style.display = 'block';

        // 标记第三步为完成
        document.getElementById('step3').classList.remove('active');
        document.getElementById('step3').classList.add('completed');
    }
}

// 隐藏选择完成状态
function hideSelectionComplete() {
    document.getElementById('selectionComplete').style.display = 'none';
    subjectSelectionState.selectedSubject = null;
}

// 显示添加明细模态框
function showAddDetailModal() {
    currentEditingDetailId = null;
    document.getElementById('detailModalLabel').textContent = '添加凭证明细';

    // 重置表单
    resetDetailForm();

    // 显示模态框
    $('#detailModal').modal('show');
}

// 编辑明细
function editDetail(detailId) {
    currentEditingDetailId = detailId;
    document.getElementById('detailModalLabel').textContent = '编辑凭证明细';

    // 获取明细数据
    const row = document.querySelector(`tr[data-detail-id="${detailId}"]`);
    if (!row) {
        alert('找不到明细记录');
        return;
    }

    // 从表格行中提取数据
    const subjectText = row.querySelector('td:nth-child(2)').textContent.trim();
    const summary = row.querySelector('td:nth-child(3)').textContent.trim();
    const debitAmountElement = row.querySelector('td:nth-child(4) .financial-amount');
    const creditAmountElement = row.querySelector('td:nth-child(5) .financial-amount');
    const auxiliaryInfo = row.querySelector('td:nth-child(6)').textContent.trim();

    const debitAmount = debitAmountElement ? parseFloat(debitAmountElement.textContent) : 0;
    const creditAmount = creditAmountElement ? parseFloat(creditAmountElement.textContent) : 0;

    // 重置表单
    resetDetailForm();

    // 填充表单数据
    document.getElementById('detailSummary').value = summary;
    document.getElementById('debitAmount').value = debitAmount > 0 ? debitAmount : '';
    document.getElementById('creditAmount').value = creditAmount > 0 ? creditAmount : '';
    document.getElementById('auxiliaryInfo').value = auxiliaryInfo;

    // TODO: 这里需要根据科目信息回显科目选择
    // 由于需要异步加载科目数据，暂时显示提示
    document.getElementById('selectionComplete').style.display = 'block';
    document.getElementById('selectedSubjectDisplay').textContent = subjectText;

    // 显示模态框
    $('#detailModal').modal('show');
}

// 删除明细
function deleteDetail(detailId) {
    if (!confirm('确定要删除这条明细记录吗？')) {
        return;
    }

    const voucherId = {{ voucher.id }};

    fetch(`{{ url_for('financial.delete_voucher_detail', voucher_id=0, detail_id=0) }}`.replace('0/details/0', `${voucherId}/details/${detailId}`), {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 移除表格行
            const row = document.querySelector(`tr[data-detail-id="${detailId}"]`);
            if (row) {
                row.remove();
            }

            // 检查是否还有明细
            const remainingRows = document.querySelectorAll('#detailsTable tbody tr[data-detail-id]');
            if (remainingRows.length === 0) {
                // 显示无明细提示
                const tbody = document.querySelector('#detailsTable tbody');
                tbody.innerHTML = `
                    <tr id="noDetailsRow">
                        <td colspan="7" class="text-center text-muted">暂无明细记录</td>
                    </tr>
                `;
            }

            // 更新合计
            updateDetailTotals();

            // 显示成功消息
            showToast('success', '明细删除成功');
        } else {
            alert('删除失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('删除明细失败:', error);
        alert('删除失败，请重试');
    });
}

// 保存明细
function saveDetail() {
    // 验证表单
    if (!validateDetailForm()) {
        return;
    }

    const formData = getDetailFormData();
    const voucherId = {{ voucher.id }};

    const saveBtn = document.getElementById('saveDetailBtn');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
    saveBtn.disabled = true;

    const url = currentEditingDetailId
        ? `{{ url_for('financial.update_voucher_detail', voucher_id=0, detail_id=0) }}`.replace('0/details/0', `${voucherId}/details/${currentEditingDetailId}`)
        : `{{ url_for('financial.add_voucher_detail', voucher_id=0) }}`.replace('/details', `/${voucherId}/details`);

    const method = currentEditingDetailId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            $('#detailModal').modal('hide');

            // 刷新页面或更新表格
            location.reload();
        } else {
            alert('保存失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('保存明细失败:', error);
        alert('保存失败，请重试');
    })
    .finally(() => {
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

// 验证明细表单
function validateDetailForm() {
    const subjectId = subjectSelectionState.selectedSubject;
    const summary = document.getElementById('detailSummary').value.trim();
    const debitAmount = parseFloat(document.getElementById('debitAmount').value) || 0;
    const creditAmount = parseFloat(document.getElementById('creditAmount').value) || 0;

    if (!subjectId) {
        alert('请选择会计科目');
        return false;
    }

    if (!summary) {
        alert('请输入摘要');
        document.getElementById('detailSummary').focus();
        return false;
    }

    if (summary.length > 200) {
        alert('摘要长度不能超过200个字符');
        document.getElementById('detailSummary').focus();
        return false;
    }

    if (debitAmount === 0 && creditAmount === 0) {
        alert('借方金额和贷方金额必须填写其中一个');
        return false;
    }

    if (debitAmount > 0 && creditAmount > 0) {
        alert('借方金额和贷方金额不能同时填写');
        return false;
    }

    if (debitAmount < 0 || creditAmount < 0) {
        alert('金额不能为负数');
        return false;
    }

    return true;
}

// 获取明细表单数据
function getDetailFormData() {
    return {
        subject_id: parseInt(subjectSelectionState.selectedSubject),
        summary: document.getElementById('detailSummary').value.trim(),
        debit_amount: parseFloat(document.getElementById('debitAmount').value) || 0,
        credit_amount: parseFloat(document.getElementById('creditAmount').value) || 0,
        auxiliary_info: document.getElementById('auxiliaryInfo').value.trim()
    };
}

// 重置明细表单
function resetDetailForm() {
    // 重置科目选择状态
    resetSubjectSelection();

    // 重置表单字段
    document.getElementById('detailForm').reset();

    // 重置字符计数
    const counter = document.getElementById('detailSummaryCounter');
    if (counter) {
        counter.textContent = '0/200 字符';
        counter.className = 'form-text text-muted';
    }
}

// 显示提示消息
function showToast(type, message) {
    // 简单的提示实现，可以根据项目需要使用 toastr 或其他库
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    // 在页面顶部显示提示
    const container = document.querySelector('.financial-content') || document.body;
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}

// 财务凭证编辑页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initVoucherEditPage();
});
</script>
{% endblock %}