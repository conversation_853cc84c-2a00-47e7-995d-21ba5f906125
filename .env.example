# 财务系统环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# 数据库配置
DATABASE_URL=mssql+pyodbc://username:password@server/database?driver=ODBC+Driver+17+for+SQL+Server

# Flask 配置
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
FLASK_DEBUG=True

# 财务系统配置
FINANCIAL_DECIMAL_PLACES=2
FINANCIAL_CURRENCY_SYMBOL=¥
FINANCIAL_TOLERANCE=0.01

# Beancount 配置
BEANCOUNT_ENABLED=True
BEANCOUNT_CURRENCY=CNY

# 报表配置
REPORTS_TEMP_DIR=temp/reports
REPORTS_MAX_EXPORT_ROWS=10000

# 安全配置
ENCRYPTION_KEY=your-encryption-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/financial.log
